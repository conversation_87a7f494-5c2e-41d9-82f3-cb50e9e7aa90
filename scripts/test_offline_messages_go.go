package main

import (
	"crypto/tls"
	"fmt"
	"log"
	"os"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

const (
	broker   = "tcp://localhost:1884"
	username = "admin"
	password = "MufongEsop666"
	clientID = "test_device_offline_001"
	topic    = "device/test_device_offline_001/command"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: go run test_offline_messages_go.go [subscribe|publish|test]")
		fmt.Println("  subscribe: 建立持久会话订阅")
		fmt.Println("  publish: 发送离线消息")
		fmt.Println("  test: 完整测试流程")
		return
	}

	command := os.Args[1]

	switch command {
	case "subscribe":
		subscribe()
	case "publish":
		publish()
	case "test":
		fullTest()
	default:
		fmt.Println("未知命令:", command)
	}
}

// 建立持久会话订阅
func subscribe() {
	fmt.Println("=== 建立持久会话订阅 ===")

	opts := mqtt.NewClientOptions()
	opts.AddBroker(broker)
	opts.SetClientID(clientID)
	opts.SetUsername(username)
	opts.SetPassword(password)
	opts.SetCleanSession(false) // 关键：设置为false以启用持久会话
	opts.SetAutoReconnect(true)
	opts.SetKeepAlive(60 * time.Second)
	opts.SetPingTimeout(10 * time.Second)
	opts.SetConnectTimeout(10 * time.Second)

	// 设置TLS配置（如果需要）
	opts.SetTLSConfig(&tls.Config{InsecureSkipVerify: true})

	// 连接回调
	opts.SetOnConnectHandler(func(client mqtt.Client) {
		fmt.Printf("✓ 已连接到MQTT服务器，客户端ID: %s\n", clientID)
		fmt.Println("✓ 持久会话已建立 (Clean Session = false)")

		// 订阅主题
		token := client.Subscribe("device/+/command", 1, messageHandler)
		if token.Wait() && token.Error() != nil {
			log.Printf("订阅失败: %v", token.Error())
			return
		}
		fmt.Printf("✓ 已订阅主题: device/+/command (QoS 1)\n")
	})

	// 连接丢失回调
	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		fmt.Printf("⚠ 连接丢失: %v\n", err)
	})

	client := mqtt.NewClient(opts)

	// 连接到服务器
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		log.Fatalf("连接失败: %v", token.Error())
	}

	fmt.Println("\n按 Ctrl+C 断开连接以模拟设备离线...")

	// 保持连接
	select {}
}

// 消息处理器
func messageHandler(client mqtt.Client, msg mqtt.Message) {
	fmt.Printf("📨 收到消息:\n")
	fmt.Printf("   主题: %s\n", msg.Topic())
	fmt.Printf("   QoS: %d\n", msg.Qos())
	fmt.Printf("   保留: %t\n", msg.Retained())
	fmt.Printf("   内容: %s\n", string(msg.Payload()))
	fmt.Printf("   时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Println("---")
}

// 发送离线消息
func publish() {
	fmt.Println("=== 发送离线消息 ===")

	opts := mqtt.NewClientOptions()
	opts.AddBroker(broker)
	opts.SetClientID("publisher_" + fmt.Sprintf("%d", time.Now().Unix()))
	opts.SetUsername(username)
	opts.SetPassword(password)
	opts.SetCleanSession(true) // 发布者可以使用Clean Session
	opts.SetConnectTimeout(10 * time.Second)
	opts.SetKeepAlive(60 * time.Second)
	opts.SetPingTimeout(10 * time.Second)

	// 添加调试回调
	opts.SetOnConnectHandler(func(client mqtt.Client) {
		fmt.Println("✓ 发布者已连接到MQTT服务器")
	})

	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		fmt.Printf("⚠ 发布者连接丢失: %v\n", err)
	})

	client := mqtt.NewClient(opts)

	fmt.Printf("正在连接到 %s，用户名: %s\n", broker, username)

	// 连接到服务器
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		log.Fatalf("连接失败: %v", token.Error())
	}
	defer client.Disconnect(250)

	fmt.Printf("✓ 发布者已连接到MQTT服务器\n")

	// 发送多条QoS 1消息
	messages := []string{
		`{"action":"restart","timestamp":"` + time.Now().Format(time.RFC3339) + `","test_id":1}`,
		`{"action":"update","version":"1.2.3","test_id":2}`,
		`{"action":"config","params":{"brightness":80},"test_id":3}`,
	}

	for i, message := range messages {
		token := client.Publish(topic, 1, false, message)
		if token.Wait() && token.Error() != nil {
			log.Printf("发送消息 %d 失败: %v", i+1, token.Error())
			continue
		}
		fmt.Printf("✓ 已发送离线消息 %d: %s\n", i+1, message)
		time.Sleep(1 * time.Second)
	}

	// 发送一条QoS 0消息用于对比
	qos0Message := `{"action":"qos0_test","message":"This QoS 0 message should NOT be stored offline","test_id":999}`
	token := client.Publish(topic, 0, false, qos0Message)
	if token.Wait() && token.Error() != nil {
		log.Printf("发送QoS 0消息失败: %v", token.Error())
	} else {
		fmt.Printf("✓ 已发送QoS 0消息（不应被存储）: %s\n", qos0Message)
	}

	fmt.Println("\n✅ 所有离线消息已发送完成")
}

// 完整测试流程
func fullTest() {
	fmt.Println("=== MQTT离线消息功能完整测试 ===")
	fmt.Println("此测试需要手动操作，请按照提示进行：")
	fmt.Println()

	fmt.Println("步骤1: 首先运行订阅客户端")
	fmt.Println("命令: go run test_offline_messages_go.go subscribe")
	fmt.Println()

	fmt.Println("步骤2: 等待订阅客户端连接成功后，按Ctrl+C断开连接")
	fmt.Println()

	fmt.Println("步骤3: 运行发布客户端发送离线消息")
	fmt.Println("命令: go run test_offline_messages_go.go publish")
	fmt.Println()

	fmt.Println("步骤4: 再次运行订阅客户端，检查是否收到离线消息")
	fmt.Println("命令: go run test_offline_messages_go.go subscribe")
	fmt.Println()

	fmt.Println("预期结果:")
	fmt.Println("- 重连后应立即收到3条QoS 1离线消息（test_id: 1, 2, 3）")
	fmt.Println("- 不应收到QoS 0消息（test_id: 999）")
	fmt.Println("- 消息接收顺序应与发送顺序一致")
}
