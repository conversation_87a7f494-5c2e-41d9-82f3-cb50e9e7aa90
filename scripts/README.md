# `/scripts`

Scripts to perform various build, install, analysis, etc operations.

These scripts keep the root level Makefile small and simple.

## Available Scripts

### test_offline_messages.sh
MQTT offline message functionality verification script.

**Features:**
- Verify MQTT server's offline message storage functionality
- Test persistent sessions vs Clean Session differences
- Compare QoS 0 vs QoS 1 message handling

**Usage:**
```bash
# Ensure MQTT server is running
./scripts/test_offline_messages.sh
```

**Prerequisites:**
1. Install mosquitto client tools
   ```bash
   # macOS
   brew install mosquitto
   
   # Ubuntu/Debian
   sudo apt-get install mosquitto-clients
   ```
2. MQTT server running (default port 1884)
3. Admin account credentials (admin/MufongEsop666)

Examples:

* https://github.com/kubernetes/helm/tree/master/scripts
* https://github.com/cockroachdb/cockroach/tree/master/scripts
* https://github.com/hashicorp/terraform/tree/master/scripts