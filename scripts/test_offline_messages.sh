#!/bin/bash

# MQTT离线消息功能验证脚本
# 使用前请确保：
# 1. mosquitto-clients已安装 (brew install mosquitto 或 apt-get install mosquitto-clients)
# 2. MQTT服务器正在运行
# 3. 具有管理员权限的账户

# 配置参数
MQTT_HOST="localhost"
MQTT_PORT="1884"
MQTT_USER="admin"
MQTT_PASS="MufongEsop666"
TEST_CLIENT_ID="test_device_001"
TEST_TOPIC="device/${TEST_CLIENT_ID}/command"

echo "=== MQTT离线消息功能测试 ==="
echo "服务器地址: ${MQTT_HOST}:${MQTT_PORT}"
echo "测试设备: ${TEST_CLIENT_ID}"
echo "测试主题: ${TEST_TOPIC}"
echo ""

# 检查mosquitto客户端是否可用
if ! command -v mosquitto_pub &> /dev/null; then
    echo "错误: mosquitto_pub 未找到。请安装 mosquitto-clients。"
    exit 1
fi

if ! command -v mosquitto_sub &> /dev/null; then
    echo "错误: mosquitto_sub 未找到。请安装 mosquitto-clients。"
    exit 1
fi

echo "第1步: 测试服务器连接..."
timeout 5 mosquitto_pub -h "$MQTT_HOST" -p "$MQTT_PORT" -t "test/connection" -m "ping" -u "$MQTT_USER" -P "$MQTT_PASS"
if [ $? -ne 0 ]; then
    echo "错误: 无法连接到MQTT服务器 ${MQTT_HOST}:${MQTT_PORT}"
    echo "请检查："
    echo "1. MQTT服务器是否正在运行"
    echo "2. 端口配置是否正确"
    echo "3. 用户名密码是否正确"
    exit 1
fi
echo "✓ 服务器连接正常"
echo ""

echo "第2步: 启动持久会话订阅..."
echo "请在新的终端窗口运行以下命令："
echo ""
echo "mosquitto_sub -h $MQTT_HOST -p $MQTT_PORT -t \"device/+/command\" -q 1 -c -i \"$TEST_CLIENT_ID\" -u \"$MQTT_USER\" -P \"$MQTT_PASS\""
echo ""
echo "运行后请按回车键继续..."
read -r

echo "第3步: 等待您手动断开订阅客户端..."
echo "请在上述订阅客户端中按Ctrl+C断开连接，然后按回车键继续..."
read -r

echo "第4步: 发送离线消息..."
MESSAGES=(
    '{"action":"restart","timestamp":"'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'","test_id":1}'
    '{"action":"update","version":"1.2.3","test_id":2}'
    '{"action":"config","params":{"brightness":80},"test_id":3}'
)

for i in "${!MESSAGES[@]}"; do
    MESSAGE="${MESSAGES[$i]}"
    echo "发送消息 $((i+1)): $MESSAGE"
    mosquitto_pub -h "$MQTT_HOST" -p "$MQTT_PORT" -t "$TEST_TOPIC" -m "$MESSAGE" -q 1 -u "$MQTT_USER" -P "$MQTT_PASS"
    sleep 1
done

echo "✓ 已发送 ${#MESSAGES[@]} 条QoS 1离线消息"
echo ""

echo "第5步: 验证离线消息接收..."
echo "现在请重新运行订阅命令（使用相同的Client ID和Clean Session=false）："
echo ""
echo "mosquitto_sub -h $MQTT_HOST -p $MQTT_PORT -t \"device/+/command\" -q 1 -c -i \"$TEST_CLIENT_ID\" -u \"$MQTT_USER\" -P \"$MQTT_PASS\""
echo ""
echo "预期结果：客户端重连后应立即收到刚才发送的3条离线消息"
echo ""

echo "第6步: 对比测试 - QoS 0消息（不应被存储）..."
echo "请按回车键继续对比测试..."
read -r

# 发送QoS 0消息用于对比
QOS0_MESSAGE='{"action":"qos0_test","message":"This QoS 0 message should NOT be stored offline","test_id":999}'
echo "发送QoS 0消息: $QOS0_MESSAGE"
mosquitto_pub -h "$MQTT_HOST" -p "$MQTT_PORT" -t "$TEST_TOPIC" -m "$QOS0_MESSAGE" -q 0 -u "$MQTT_USER" -P "$MQTT_PASS"

echo ""
echo "=== 测试完成 ==="
echo ""
echo "验证要点："
echo "1. 重连后应收到3条QoS 1离线消息（test_id: 1, 2, 3）"
echo "2. 不应收到QoS 0消息（test_id: 999）"
echo "3. 消息接收顺序应与发送顺序一致"
echo ""
echo "如果测试失败，请检查："
echo "1. Client ID是否保持一致"
echo "2. 是否使用Clean Session=false (-c参数)"
echo "3. 是否使用QoS ≥ 1"
echo "4. Badger存储是否正确配置"
echo ""
echo "存储目录检查："
echo "ls -la storage/mqtt/badger/"
ls -la storage/mqtt/badger/ 2>/dev/null || echo "存储目录不存在或无法访问"