package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	badgerdb "github.com/dgraph-io/badger/v4"
)

func main() {
	fmt.Println("=== MQTT离线消息诊断工具 ===")
	fmt.Println()

	// 检查存储目录
	checkStorageDirectory()

	// 检查Badger数据库
	checkBadgerDatabase()

	// 检查配置文件
	checkConfigFiles()
}

func checkStorageDirectory() {
	fmt.Println("1. 检查存储目录...")

	storageDir := "storage/mqtt"
	badgerDir := filepath.Join(storageDir, "badger")

	// 检查主存储目录
	if info, err := os.Stat(storageDir); err != nil {
		fmt.Printf("❌ 存储目录不存在: %s\n", storageDir)
		return
	} else {
		fmt.Printf("✓ 存储目录存在: %s (权限: %v)\n", storageDir, info.Mode())
	}

	// 检查Badger目录
	if info, err := os.Stat(badgerDir); err != nil {
		fmt.Printf("❌ Badger目录不存在: %s\n", badgerDir)
		return
	} else {
		fmt.Printf("✓ Badger目录存在: %s (权限: %v)\n", badgerDir, info.Mode())
	}

	// 列出Badger目录内容
	files, err := os.ReadDir(badgerDir)
	if err != nil {
		fmt.Printf("❌ 无法读取Badger目录: %v\n", err)
		return
	}

	fmt.Printf("✓ Badger目录包含 %d 个文件:\n", len(files))
	for _, file := range files {
		info, _ := file.Info()
		fmt.Printf("  - %s (%d bytes, %v)\n", file.Name(), info.Size(), info.ModTime())
	}
	fmt.Println()
}

func checkBadgerDatabase() {
	fmt.Println("2. 检查Badger数据库内容...")

	badgerDir := "storage/mqtt/badger"

	// 打开Badger数据库
	opts := badgerdb.DefaultOptions(badgerDir)
	opts.Logger = nil // 禁用日志输出

	db, err := badgerdb.Open(opts)
	if err != nil {
		fmt.Printf("❌ 无法打开Badger数据库: %v\n", err)
		return
	}
	defer db.Close()

	fmt.Println("✓ 成功打开Badger数据库")

	// 统计不同类型的数据
	counts := map[string]int{
		"client":       0,
		"subscription": 0,
		"retained":     0,
		"inflight":     0,
		"sysinfo":      0,
		"other":        0,
	}

	err = db.View(func(txn *badgerdb.Txn) error {
		iterator := txn.NewIterator(badgerdb.DefaultIteratorOptions)
		defer iterator.Close()

		for iterator.Rewind(); iterator.Valid(); iterator.Next() {
			item := iterator.Item()
			key := string(item.Key())

			switch {
			case contains(key, "client"):
				counts["client"]++
			case contains(key, "subscription"):
				counts["subscription"]++
			case contains(key, "retained"):
				counts["retained"]++
			case contains(key, "inflight"):
				counts["inflight"]++
			case contains(key, "sysinfo"):
				counts["sysinfo"]++
			default:
				counts["other"]++
			}
		}
		return nil
	})
	if err != nil {
		fmt.Printf("❌ 读取数据库失败: %v\n", err)
		return
	}

	fmt.Println("✓ 数据库内容统计:")
	for dataType, count := range counts {
		if count > 0 {
			fmt.Printf("  - %s: %d 条记录\n", dataType, count)
		}
	}

	// 显示最近的一些键
	fmt.Println("✓ 最近的数据库键（最多显示10个）:")
	count := 0
	err = db.View(func(txn *badgerdb.Txn) error {
		iterator := txn.NewIterator(badgerdb.DefaultIteratorOptions)
		defer iterator.Close()

		for iterator.Rewind(); iterator.Valid() && count < 10; iterator.Next() {
			item := iterator.Item()
			key := string(item.Key())
			fmt.Printf("  - %s\n", key)
			count++
		}
		return nil
	})
	if err != nil {
		fmt.Printf("❌ 读取键失败: %v\n", err)
	}

	fmt.Println()
}

func checkConfigFiles() {
	fmt.Println("3. 检查配置文件...")

	configFiles := []string{
		"config/local.yml",
		"config/prod.yml",
	}

	for _, configFile := range configFiles {
		if info, err := os.Stat(configFile); err != nil {
			fmt.Printf("❌ 配置文件不存在: %s\n", configFile)
		} else {
			fmt.Printf("✓ 配置文件存在: %s (%d bytes, %v)\n",
				configFile, info.Size(), info.ModTime())
		}
	}

	fmt.Println()
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[:len(substr)] == substr
}

// 额外的诊断功能
func init() {
	log.SetFlags(log.LstdFlags | log.Lshortfile)
}
