# MQTT离线消息问题分析与解决方案

## 问题描述

在测试过程中发现，MQTT在设备离线期间下发的信息，在设备重新启动程序后没有接收到。具体表现为：
1. 设备程序关闭（模拟离线）
2. 管理后台下发信息
3. 设备程序重新启动
4. 设备没有收到离线期间发送的消息

## 问题分析

经过代码分析，发现以下几个可能的问题：

### 1. MQTT服务器配置不完整

**问题**: 原始配置过于简单，缺少关键的离线消息处理配置
```go
// 原始配置
options := &mqtt.Options{
    InlineClient: true,
}
```

**解决方案**: 添加完整的服务器配置
```go
options := &mqtt.Options{
    InlineClient: true,
    Capabilities: &mqtt.Capabilities{
        MaximumSessionExpiryInterval: 86400,  // 24小时会话过期
        MaximumMessageExpiryInterval: 86400,  // 24小时消息过期
        MaximumClientWritesPending: 1024,     // 最大待发送消息数
        ReceiveMaximum: 65535,                // QoS流控配额
        TopicAliasMaximum: 65535,             // 主题别名
        Compatibilities: mqtt.Compatibilities{
            PassiveClientDisconnect: false,
            ObscureNotAuthorized: false,
        },
    },
    ClientNetWriteBufferSize: 4096,
    ClientNetReadBufferSize:  4096,
    SysTopicResendInterval: 30,
}
```

### 2. 客户端连接配置问题

**关键要求**:
- 客户端必须使用 `Clean Session = false`
- 客户端ID必须保持一致
- 消息QoS必须 ≥ 1
- 订阅QoS必须 ≥ 1

### 3. Badger存储配置

当前Badger配置基本正确，但可以优化：
```go
badgerOptions := &badger.Options{
    Path: storageDir + "/badger",
    GcInterval:      300,  // 5分钟GC
    GcDiscardRatio:  0.5,  // 50%丢弃率
}
```

## 解决方案实施

### 1. 服务器配置修改

已修改 `internal/mqtt/server.go`，添加完整的服务器配置选项。

### 2. 配置文件增强

已修改 `config/local.yml`，添加离线消息相关配置：
```yaml
mqtt_server:
  tcp_host: 0.0.0.0
  tcp_port: 1884
  authentication:
    enabled: false
    allow_anonymous: false
  offline_messages:
    enabled: true
    max_stored_messages: 1000
    message_expiry_seconds: 86400
  session:
    max_expiry_seconds: 86400
    persistent_storage: true
```

### 3. 测试工具

创建了两个测试工具：

#### a) Go语言测试客户端 (`scripts/test_offline_messages_go.go`)
```bash
# 建立持久会话订阅
go run scripts/test_offline_messages_go.go subscribe

# 发送离线消息
go run scripts/test_offline_messages_go.go publish

# 查看完整测试流程
go run scripts/test_offline_messages_go.go test
```

#### b) 诊断工具 (`scripts/mqtt_diagnostic.go`)
```bash
go run scripts/mqtt_diagnostic.go
```

## 测试步骤

### 1. 启动MQTT服务器
```bash
cd cmd
go build -o mqtt && ./mqtt
```

### 2. 运行诊断工具
```bash
go run scripts/mqtt_diagnostic.go
```

### 3. 测试离线消息功能

#### 步骤1: 建立持久会话
```bash
go run scripts/test_offline_messages_go.go subscribe
```
等待连接成功后，按 Ctrl+C 断开连接。

#### 步骤2: 发送离线消息
```bash
go run scripts/test_offline_messages_go.go publish
```

#### 步骤3: 重新连接验证
```bash
go run scripts/test_offline_messages_go.go subscribe
```

### 预期结果
- 重连后应立即收到3条QoS 1离线消息（test_id: 1, 2, 3）
- 不应收到QoS 0消息（test_id: 999）
- 消息接收顺序应与发送顺序一致

## 故障排查

### 1. 检查存储目录
```bash
ls -la storage/mqtt/badger/
```

### 2. 检查服务器日志
查看 `storage/logs/server.log` 中的MQTT相关日志。

### 3. 验证客户端配置
确保客户端使用：
- Clean Session = false
- 相同的Client ID
- QoS ≥ 1

### 4. 检查数据库内容
使用诊断工具检查Badger数据库是否正确存储了会话和消息数据。

## 常见问题

### Q1: 离线消息未被存储
**原因**: 
- 消息QoS等级为0
- 客户端使用Clean Session=true
- Badger存储路径权限问题

**解决**: 
- 确保消息QoS ≥ 1
- 客户端连接时设置Clean Session=false
- 检查storage/mqtt/badger目录权限

### Q2: 设备重连后收不到离线消息
**原因**:
- 客户端ID发生变化
- 重连时使用了Clean Session=true
- 订阅主题不匹配

**解决**:
- 确保重连时使用相同的Client ID
- 重连时设置Clean Session=false
- 检查主题订阅是否正确

### Q3: 存储空间过大
**原因**:
- Badger GC未正常运行
- 大量离线消息累积

**解决**:
- 检查GcInterval和GcDiscardRatio配置
- 定期清理过期消息
- 监控storage/mqtt/badger目录大小

## 生产环境建议

1. **监控存储使用**: 定期检查badger存储目录大小
2. **设置消息过期**: 配置合理的消息过期时间
3. **限制离线消息数量**: 避免单个设备离线消息过多
4. **备份策略**: 定期备份badger存储数据
5. **性能监控**: 监控MQTT服务器性能指标
