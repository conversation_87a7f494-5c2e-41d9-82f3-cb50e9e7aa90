env: local
http:
  host: 0.0.0.0
  port: 8567
data:
  db:
    driver: sqlite
    dsn: db/esop.db?_pragma_key=%s&_pragma_cipher_page_size=%d

log:
  log_level: debug
  encoding: console # json or console
  log_file_name: "./storage/logs/server.log"
  max_backups: 30
  max_age: 7
  max_size: 1024
  compress: true

mqtt:
  host: "127.0.0.1"
  port: 1884
  client_id: "esop_server"
  qos: 1
  retain: true

mqtt_server:
  tcp_host: 0.0.0.0
  tcp_port: 1884 # 使用一个新端口，避免与可能存在的其他MQTT服务冲突
  authentication:
    enabled: false # 开启认证
    allow_anonymous: false # 不允许匿名访问
  # 离线消息和会话配置
  offline_messages:
    enabled: true # 启用离线消息存储
    max_stored_messages: 1000 # 每个客户端最大存储消息数
    message_expiry_seconds: 86400 # 消息过期时间（24小时）
  session:
    max_expiry_seconds: 86400 # 最大会话过期时间（24小时）
    persistent_storage: true # 启用持久化存储
