package mqtt

import (
	"fmt"

	"os"

	mqtt "github.com/mochi-mqtt/server/v2"
	"github.com/mochi-mqtt/server/v2/hooks/storage/badger"
	"github.com/mochi-mqtt/server/v2/listeners"
	"github.com/spf13/viper"
)

// NewMqttServer creates and configures a new MQTT server.

func NewMqttServer(conf *viper.Viper) (*mqtt.Server, error) {
	// 从配置中读取 MQTT 服务端设置
	tcpHost := conf.GetString("mqtt_server.tcp_host")
	tcpPort := conf.GetInt("mqtt_server.tcp_port")
	// 创建一个新的 MQTT 服务实例
	options := &mqtt.Options{
		InlineClient: true,
	}
	server := mqtt.New(options)

	// Add our custom license auth hook. This hook is responsible for all authentication.
	// The OnConnectAuthenticate method within this hook checks the database for valid
	// credentials and also handles logic for license expiration and terminal limits.
	// Crucially, it returns false if a username/password is not provided.
	if err := server.AddHook(newLicenseAuthHook(conf), nil); err != nil {
		return nil, fmt.Errorf("failed to add license auth hook: %w", err)
	}

	// Add badger storage hook for persistence.
	storageDir := "storage/mqtt"
	if _, err := os.Stat(storageDir); os.IsNotExist(err) {
		os.MkdirAll(storageDir, 0755)
	}
	// Add badger storage hook for persistence with comprehensive configuration
	// This ensures proper offline message storage, session persistence, and QoS handling
	badgerHook := new(badger.Hook)
	badgerOptions := &badger.Options{
		Path: storageDir + "/badger",
		// Enable garbage collection to manage storage efficiently
		GcInterval:      300, // Run GC every 5 minutes (300 seconds)
		GcDiscardRatio:  0.5, // Discard 50% of eligible data during GC
		// These settings ensure proper persistence for:
		// - Client sessions (when Clean Session = false)
		// - QoS 1 and QoS 2 messages for offline clients
		// - Retained messages
		// - Subscription information
	}
	if err := server.AddHook(badgerHook, badgerOptions); err != nil {
		return nil, fmt.Errorf("failed to add badger hook: %w", err)
	}

	// 配置 TCP 监听器
	tcpAddr := fmt.Sprintf("%s:%d", tcpHost, tcpPort)
	tcp := listeners.NewTCP(listeners.Config{
		ID:      "tcp1",
		Address: tcpAddr,
	})

	if err := server.AddListener(tcp); err != nil {
		return nil, fmt.Errorf("failed to add TCP listener: %w", err)
	}

	return server, nil
}