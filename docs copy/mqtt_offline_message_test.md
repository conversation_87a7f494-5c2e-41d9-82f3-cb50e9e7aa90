# MQTT离线消息功能测试指南

## 测试目的
验证mochi-mqtt服务器的离线消息存储和推送功能是否正常工作。

## 前提条件
1. MQTT服务器已启动并运行在配置的端口（默认1884）
2. Badger持久化Hook已正确配置
3. 测试客户端支持Clean Session标志设置

## 测试场景

### 场景1：基本离线消息测试

#### 步骤1：设备A建立持久会话
```bash
# 使用mosquitto客户端连接，设置Clean Session=false
mosquitto_sub -h localhost -p 1884 -t "device/+/command" -q 1 -c -i "device_001" -u "admin" -P "MufongEsop666"
```

参数说明：
- `-c`: 设置Clean Session=false，启用持久会话
- `-q 1`: 设置QoS=1，确保消息可靠传输
- `-i "device_001"`: 客户端ID
- `-t "device/+/command"`: 订阅主题

#### 步骤2：模拟设备A断线
强制关闭上述mosquitto_sub进程（Ctrl+C）

#### 步骤3：发送离线消息
```bash
# 向设备A发送QoS 1消息
mosquitto_pub -h localhost -p 1884 -t "device/device_001/command" -m '{"action":"restart","timestamp":"2025-01-20T10:00:00Z"}' -q 1 -u "admin" -P "MufongEsop666"

mosquitto_pub -h localhost -p 1884 -t "device/device_001/command" -m '{"action":"update","version":"1.2.3"}' -q 1 -u "admin" -P "MufongEsop666"

mosquitto_pub -h localhost -p 1884 -t "device/device_001/command" -m '{"action":"config","params":{"brightness":80}}' -q 1 -u "admin" -P "MufongEsop666"
```

#### 步骤4：设备A重新上线
```bash
# 使用相同的Client ID重新连接，Clean Session=false
mosquitto_sub -h localhost -p 1884 -t "device/+/command" -q 1 -c -i "device_001" -u "admin" -P "MufongEsop666"
```

#### 预期结果
设备A重新连接后，应该立即收到步骤3中发送的所有3条离线消息。

### 场景2：QoS 0消息测试（对比组）

#### 步骤1：设备B建立持久会话
```bash
mosquitto_sub -h localhost -p 1884 -t "device/+/notification" -q 0 -c -i "device_002" -u "admin" -P "MufongEsop666"
```

#### 步骤2：设备B断线后发送QoS 0消息
```bash
# 发送QoS 0消息（应该不会被存储）
mosquitto_pub -h localhost -p 1884 -t "device/device_002/notification" -m '{"message":"This QoS 0 message should be lost"}' -q 0 -u "admin" -P "MufongEsop666"
```

#### 步骤3：设备B重新上线
```bash
mosquitto_sub -h localhost -p 1884 -t "device/+/notification" -q 0 -c -i "device_002" -u "admin" -P "MufongEsop666"
```

#### 预期结果
设备B重新连接后，不应该收到QoS 0的离线消息（因为QoS 0不保证消息送达）。

### 场景3：Clean Session=true测试（对比组）

#### 步骤1：设备C建立临时会话
```bash
# 注意：不使用-c参数，默认Clean Session=true
mosquitto_sub -h localhost -p 1884 -t "device/+/command" -q 1 -i "device_003" -u "admin" -P "MufongEsop666"
```

#### 步骤2：设备C断线后发送消息
```bash
mosquitto_pub -h localhost -p 1884 -t "device/device_003/command" -m '{"action":"test","clean_session":true}' -q 1 -u "admin" -P "MufongEsop666"
```

#### 步骤3：设备C重新上线
```bash
mosquitto_sub -h localhost -p 1884 -t "device/+/command" -q 1 -i "device_003" -u "admin" -P "MufongEsop666"
```

#### 预期结果
设备C重新连接后，不应该收到离线消息（因为Clean Session=true会清除会话状态）。

## 验证方法

### 1. 检查Badger存储
```bash
# 检查badger存储目录是否存在且有数据
ls -la storage/mqtt/badger/
```

### 2. 查看服务器日志
启动MQTT服务器时应该看到类似日志：
```
Starting MQTT server...
Badger storage hook initialized at: storage/mqtt/badger
```

### 3. 监控连接状态
在license_hook.go中已实现连接/断开事件的日志输出，观察控制台输出：
```
MQTT OnConnected called for client: device_001
MQTT OnDisconnect called for client: device_001
```

## 故障排查

### 问题1：离线消息未被存储
**可能原因：**
- 消息QoS等级为0
- 客户端使用Clean Session=true连接
- Badger存储路径权限问题

**解决方案：**
1. 确保消息QoS ≥ 1
2. 客户端连接时设置Clean Session=false
3. 检查storage/mqtt/badger目录权限

### 问题2：设备重连后收不到离线消息
**可能原因：**
- 客户端ID发生变化
- 重连时使用了Clean Session=true
- 订阅主题不匹配

**解决方案：**
1. 确保重连时使用相同的Client ID
2. 重连时设置Clean Session=false
3. 检查主题订阅是否正确

### 问题3：存储空间过大
**可能原因：**
- Badger GC未正常运行
- 大量离线消息累积

**解决方案：**
1. 检查GcInterval和GcDiscardRatio配置
2. 定期清理过期消息
3. 监控storage/mqtt/badger目录大小

## 重要注意事项

1. **QoS要求**：只有QoS 1和QoS 2的消息才会被存储为离线消息
2. **Clean Session**：客户端必须使用Clean Session=false才能启用持久会话
3. **Client ID**：重连时必须使用相同的Client ID才能恢复会话
4. **存储限制**：应合理设置消息过期时间，避免存储空间无限增长
5. **性能影响**：大量离线消息可能影响设备上线时的性能

## 生产环境建议

1. **监控存储使用**：定期检查badger存储目录大小
2. **设置消息过期**：配置合理的消息过期时间
3. **限制离线消息数量**：避免单个设备离线消息过多
4. **备份策略**：定期备份badger存储数据